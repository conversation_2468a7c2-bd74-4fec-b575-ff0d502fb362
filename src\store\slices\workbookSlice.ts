import { toast } from 'react-toastify';

import {
  RetrievalWorkbook,
  RetrievalWorkbookUpdate,
  RetrievalWorkbookCreate,
  RetrievalWorkbookUpdateResponse,
  RetrievalFileSignedUrlUploadResponse,
  RetrievalWorkbookQueryRequest,
  RetrievalSessionQuery,
  RetrievalWorkbookMessageFeedbackRequest,
  RetrievalSessionAnswer,
  AddMockLoadingFile,
  RemoveMockLoadingFile,
  RetrievalFile,
  RetrievalSession,
} from '@features/workbook/workbookTypes';

import { createSidekickSlice } from '../withTypes';

import {
  postWorkbook,
  postPublicWorkbook,
  getUserWorkbooks,
  getGlobalWorkbooks,
  getUserWorkbookById,
  getGlobalWorkbookById,
  patchWorkbookById,
  deleteWorkbookById,
  deleteWorkbookFileById,
  GetWorkbooksResponse,
  postFileCallback,
  postFilesCallback,
  getFileIndexStatus,
  queryUserWorkbookById,
  queryGlobalWorkbookById,
  sendFeedbackByMessageId,
  postSession,
} from '../../api/workbookApi';

export interface WorkbookPendingState {
  isFetching?: boolean;
  isUpdating?: boolean;
  isDeleting?: boolean;

  filesPendingState: Record<string, WorkbookFilePendingState>;
  sessionsPendingState: Record<string, WorkbookSessionPendingState>;
}

export interface WorkbookFilePendingState {
  isDeleting?: boolean;
  isUploading?: boolean;
  isIndexing?: boolean;
  isUpdating?: boolean;
}

export interface WorkbookSessionPendingState {
  isDeleting?: boolean;
  isCreating?: boolean;
  isQuerying?: boolean;
}

export interface WorkbooksState {
  globalWorkbooks: Record<string, RetrievalWorkbook>;
  userWorkbooks: Record<string, RetrievalWorkbook>;
  userWorkbooksMockLoadingFiles: Record<string, RetrievalFile[]>;
  globalWorkbooksMockLoadingFiles: Record<string, RetrievalFile[]>;
  currentWorkbookId: string | null;
  currentWorkbookIsGlobal: boolean;
  workbookQueryInProgress: boolean;
  globalWorkbookPendingState: Record<string, WorkbookPendingState>;
  userWorkbookPendingState: Record<string, WorkbookPendingState>;
}

const initialState: WorkbooksState = {
  globalWorkbooks: {},
  userWorkbooks: {},
  userWorkbooksMockLoadingFiles: {},
  globalWorkbooksMockLoadingFiles: {},
  currentWorkbookId: null,
  currentWorkbookIsGlobal: false,
  workbookQueryInProgress: false,
  globalWorkbookPendingState: {},
  userWorkbookPendingState: {},
};

const workbookSlice = createSidekickSlice({
  name: 'workbooks',
  initialState: initialState,
  reducers: create => {
    return {
      createMyWorkbook: create.asyncThunk(
        async (toCreate: RetrievalWorkbookCreate) => {
          const data = await postWorkbook(toCreate);
          return data;
        },
        {
          pending: (_, action) => {
            const toCreate = action.meta.arg;
            toast.loading(`Creating workbook with name: ${toCreate.name}...`, {
              toastId: `create-my-workbook-${toCreate.name}`,
            });
          },
          fulfilled: (state, action) => {
            const { workbook } = action.payload;
            state.userWorkbooks[workbook.id] = workbook;
            state.userWorkbookPendingState[workbook.id] = {
              filesPendingState: {},
              sessionsPendingState: {},
            };

            const workbookPendingState = state.userWorkbookPendingState[workbook.id];
            for (const file of workbook.files) {
              workbookPendingState.filesPendingState[file.id] = {};
            }
            for (const session of workbook.sessions) {
              workbookPendingState.sessionsPendingState[session.id] = {};
            }
            toast.dismiss(`create-my-workbook-${workbook.name}`);
          },
          rejected: (_, action) => {
            const toCreate = action.meta.arg;
            if (action.error.message && action.error.message === 'CONFLICT') {
              toast.error(`Workbook with name "${toCreate.name}" already exists!`);
            } else {
              toast.error(`Failed to create workbook with name "${toCreate.name}"`);
            }
            toast.dismiss(`create-my-workbook-${toCreate.name}`);
          },
        }
      ),
      createPublicWorkbook: create.asyncThunk(
        async (toCreate: RetrievalWorkbookCreate) => {
          const data = await postPublicWorkbook(toCreate);
          return data;
        },
        {
          pending: (_, action) => {
            const toCreate = action.meta.arg;
            toast.loading(`Creating workbook with name: ${toCreate.name}...`, {
              toastId: `create-public-workbook-${toCreate.name}`,
            });
          },
          fulfilled: (state, action) => {
            const { workbook } = action.payload;
            state.globalWorkbooks[workbook.id] = workbook;
            state.globalWorkbookPendingState[workbook.id] = {
              filesPendingState: {},
              sessionsPendingState: {},
            };

            const workbookPendingState = state.globalWorkbookPendingState[workbook.id];
            for (const file of workbook.files) {
              workbookPendingState.filesPendingState[file.id] = {};
            }
            for (const session of workbook.sessions) {
              workbookPendingState.sessionsPendingState[session.id] = {};
            }
            toast.dismiss(`create-public-workbook-${workbook.name}`);
          },
          rejected: (_, action) => {
            const toCreate = action.meta.arg;
            if (action.error.message && action.error.message === 'CONFLICT') {
              toast.error(`Public workbook with name "${toCreate.name}" already exists!`);
            } else {
              toast.error(`Failed to create public workbook with name "${toCreate.name}"`);
            }
            toast.dismiss(`create-public-workbook-${toCreate.name}`);
          },
        }
      ),
      createMyWorkbookSession: create.asyncThunk(
        async ({ workbookId, isGlobal = false }: { workbookId: string; isGlobal: boolean }) => {
          const data = await postSession(workbookId, isGlobal);
          return data;
        },
        {
          fulfilled: (state, action) => {
            const { isGlobal } = action.meta.arg;
            const { workbookId, session } = action.payload;
            const workbooks = isGlobal ? state.globalWorkbooks : state.userWorkbooks;
            const pendingState = isGlobal
              ? state.globalWorkbookPendingState
              : state.userWorkbookPendingState;
            workbooks[workbookId].sessions.push(session);
            pendingState[workbookId].sessionsPendingState[session.id] = {};
          },
        }
      ),
      fetchMyWorkbooks: create.asyncThunk(
        async () => {
          const data = await getUserWorkbooks();
          return data;
        },
        {
          fulfilled: (state, action) => {
            const data: GetWorkbooksResponse = action.payload;
            const { workbooks } = data;

            state.userWorkbooks = workbooks.reduce(
              (acc, curr) => {
                acc[curr.id] = curr;
                return acc;
              },
              {} as Record<string, RetrievalWorkbook>
            );
            state.userWorkbookPendingState = workbooks.reduce(
              (acc, curr) => {
                const pendingState: WorkbookPendingState = {
                  filesPendingState: {},
                  sessionsPendingState: {},
                };
                for (const file of curr.files) {
                  pendingState.filesPendingState[file.id] = {};
                }
                for (const session of curr.sessions) {
                  pendingState.sessionsPendingState[session.id] = {};
                }
                acc[curr.id] = pendingState;
                return acc;
              },
              {} as Record<string, WorkbookPendingState>
            );
          },
        }
      ),
      fetchGlobalWorkbooks: create.asyncThunk(
        async () => {
          const data = await getGlobalWorkbooks();
          return data;
        },
        {
          fulfilled: (state, action) => {
            const data: GetWorkbooksResponse = action.payload;
            const { workbooks } = data;

            state.globalWorkbooks = workbooks.reduce(
              (acc, curr) => {
                acc[curr.id] = curr;
                return acc;
              },
              {} as Record<string, RetrievalWorkbook>
            );
            state.globalWorkbookPendingState = workbooks.reduce(
              (acc, curr) => {
                const pendingState: WorkbookPendingState = {
                  isDeleting: false,
                  isFetching: false,
                  isUpdating: false,
                  filesPendingState: {},
                  sessionsPendingState: {},
                };
                for (const file of curr.files) {
                  pendingState.filesPendingState[file.id] = {};
                }
                for (const session of curr.sessions) {
                  pendingState.sessionsPendingState[session.id] = {};
                }
                acc[curr.id] = pendingState;
                return acc;
              },
              {} as Record<string, WorkbookPendingState>
            );
          },
        }
      ),
      fetchMyWorkbookById: create.asyncThunk(
        async (workbookId: string) => {
          const workbook = await getUserWorkbookById(workbookId);
          return workbook;
        },
        {
          pending: (state, action) => {
            const workbookId: string = action.meta.arg;
            if (!state.userWorkbookPendingState[workbookId]) {
              state.userWorkbookPendingState[workbookId] = {
                filesPendingState: {},
                sessionsPendingState: {},
              };
            }
            state.userWorkbookPendingState[workbookId].isFetching = true;
          },
          fulfilled: (state, action) => {
            const workbook: RetrievalWorkbook = action.payload;
            state.userWorkbooks[workbook.id] = workbook;
            state.currentWorkbookId = workbook.id;
            state.currentWorkbookIsGlobal = false;

            for (const file of workbook.files) {
              state.userWorkbookPendingState[workbook.id].filesPendingState[file.id] = {};
            }
            for (const session of workbook.sessions) {
              state.userWorkbookPendingState[workbook.id].sessionsPendingState[session.id] = {};
            }
            state.userWorkbookPendingState[workbook.id].isFetching = false;
          },
          rejected: (state, action) => {
            const workbookId: string = action.meta.arg;
            state.userWorkbookPendingState[workbookId].isFetching = false;
          },
        }
      ),
      fetchGlobalWorkbookById: create.asyncThunk(
        async (workbookId: string) => {
          const workbook = await getGlobalWorkbookById(workbookId);
          return workbook;
        },
        {
          pending: (state, action) => {
            const workbookId: string = action.meta.arg;
            if (!state.globalWorkbookPendingState[workbookId]) {
              state.globalWorkbookPendingState[workbookId] = {
                filesPendingState: {},
                sessionsPendingState: {},
              };
            }
            state.globalWorkbookPendingState[workbookId].isFetching = true;
          },
          fulfilled: (state, action) => {
            const workbook: RetrievalWorkbook = action.payload;
            state.globalWorkbooks[workbook.id] = workbook;
            state.currentWorkbookId = workbook.id;
            state.currentWorkbookIsGlobal = true;

            for (const file of workbook.files) {
              state.globalWorkbookPendingState[workbook.id].filesPendingState[file.id] = {};
            }
            for (const session of workbook.sessions) {
              state.globalWorkbookPendingState[workbook.id].sessionsPendingState[session.id] = {};
            }
            state.globalWorkbookPendingState[workbook.id].isFetching = false;
          },
          rejected: (state, action) => {
            const workbookId: string = action.meta.arg;
            state.globalWorkbookPendingState[workbookId].isFetching = false;
          },
        }
      ),
      updateWorkbookById: create.asyncThunk(
        async ({
          workbookUpdates,
          isGlobal = false,
        }: {
          workbookUpdates: RetrievalWorkbookUpdate;
          isGlobal: boolean;
        }) => {
          const updatedWorkbookFields: RetrievalWorkbookUpdateResponse = await patchWorkbookById(
            workbookUpdates,
            isGlobal
          );
          return updatedWorkbookFields;
        },
        {
          pending: (state, action) => {
            const { workbookUpdates, isGlobal } = action.meta.arg;
            const pendingState = isGlobal
              ? state.globalWorkbookPendingState
              : state.userWorkbookPendingState;
            pendingState[workbookUpdates.id].isUpdating = true;
          },
          fulfilled: (state, action) => {
            const updatedFields = action.payload;
            const { isGlobal } = action.meta.arg;
            const workbooks = isGlobal ? state.globalWorkbooks : state.userWorkbooks;
            const pendingState = isGlobal
              ? state.globalWorkbookPendingState
              : state.userWorkbookPendingState;

            const existingWorkbook = workbooks[updatedFields.id];
            if (existingWorkbook) {
              workbooks[existingWorkbook.id] = Object.assign(existingWorkbook, updatedFields);
            }
            pendingState[existingWorkbook.id].isUpdating = false;
          },
          rejected: (state, action) => {
            const { workbookUpdates, isGlobal } = action.meta.arg;
            const pendingState = isGlobal
              ? state.globalWorkbookPendingState
              : state.userWorkbookPendingState;
            pendingState[workbookUpdates.id].isUpdating = false;
          },
        }
      ),
      removeWorkbookById: create.asyncThunk(
        async ({ workbookId, isGlobal = false }: { workbookId: string; isGlobal: boolean }) => {
          await deleteWorkbookById(workbookId, isGlobal);
          return workbookId;
        },
        {
          pending: (state, action) => {
            const { workbookId, isGlobal } = action.meta.arg;
            const pendingState = isGlobal
              ? state.globalWorkbookPendingState
              : state.userWorkbookPendingState;
            pendingState[workbookId].isDeleting = true;
          },
          fulfilled: (state, action) => {
            const removedId = action.payload;
            const { isGlobal } = action.meta.arg;
            const pendingState = isGlobal
              ? state.globalWorkbookPendingState
              : state.userWorkbookPendingState;
            const workbooks = isGlobal ? state.globalWorkbooks : state.userWorkbooks;
            pendingState[removedId].isDeleting = false;
            delete pendingState[removedId];
            delete workbooks[removedId];
          },
          rejected: (state, action) => {
            const { workbookId, isGlobal } = action.meta.arg;
            const pendingState = isGlobal
              ? state.globalWorkbookPendingState
              : state.userWorkbookPendingState;
            pendingState[workbookId].isDeleting = false;
          },
        }
      ),
      removeWorkbookFileById: create.asyncThunk(
        async ({
          workbookId,
          fileId,
          isGlobal,
        }: {
          workbookId: string;
          fileId: string;
          isGlobal: boolean;
        }) => {
          const deleteResult = await deleteWorkbookFileById(workbookId, fileId, isGlobal);
          return deleteResult;
        },
        {
          pending: (state, action) => {
            const { workbookId, fileId, isGlobal } = action.meta.arg;
            const pendingState = isGlobal
              ? state.globalWorkbookPendingState
              : state.userWorkbookPendingState;
            pendingState[workbookId].filesPendingState[fileId].isDeleting = true;
          },
          fulfilled: (state, action) => {
            const { isGlobal } = action.meta.arg;
            const { workbookId, fileId } = action.payload;
            const workbooks = isGlobal ? state.globalWorkbooks : state.userWorkbooks;
            const pendingState = isGlobal
              ? state.globalWorkbookPendingState
              : state.userWorkbookPendingState;
            const filteredFiles = workbooks[workbookId].files.filter(f => f.id !== fileId);
            workbooks[workbookId].files = filteredFiles;
            pendingState[workbookId].filesPendingState[fileId].isDeleting = false;
            delete pendingState[workbookId].filesPendingState[fileId];
          },
          rejected: (state, action) => {
            const { workbookId, fileId, isGlobal } = action.meta.arg;
            const pendingState = isGlobal
              ? state.globalWorkbookPendingState
              : state.userWorkbookPendingState;
            pendingState[workbookId].filesPendingState[fileId].isDeleting = false;
          },
        }
      ),
      addMockLoadingFile: create.reducer<AddMockLoadingFile>((state, action) => {
        const { workbook, file, isGlobal } = action.payload;
        const workbooksMockLoadingFiles = isGlobal
          ? state.globalWorkbooksMockLoadingFiles
          : state.userWorkbooksMockLoadingFiles;
        if (workbook) {
          if (workbooksMockLoadingFiles[workbook.id]) {
            if (!workbooksMockLoadingFiles[workbook.id].find(f => f.name === file.name)) {
              workbooksMockLoadingFiles[workbook.id].push(file);
            }
          } else {
            workbooksMockLoadingFiles[workbook.id] = [file];
          }
        }
      }),
      removeMockLoadingFile: create.reducer<RemoveMockLoadingFile>((state, action) => {
        const { workbookId, fileName, isGlobal } = action.payload;
        const workbooksMockLoadingFiles = isGlobal
          ? state.globalWorkbooksMockLoadingFiles
          : state.userWorkbooksMockLoadingFiles;
        if (workbooksMockLoadingFiles[workbookId]) {
          workbooksMockLoadingFiles[workbookId] = workbooksMockLoadingFiles[workbookId].filter(
            f => f.name !== fileName
          );
        }
      }),
      callbackUploadFile: create.asyncThunk(
        async ({
          workbookId,
          fileInfo,
          isGlobal,
        }: {
          workbookId: string;
          fileInfo: RetrievalFileSignedUrlUploadResponse;
          isGlobal: boolean;
        }) => {
          const response = await postFileCallback(workbookId, fileInfo, isGlobal);
          return response;
        },
        {
          fulfilled: (state, action) => {
            const { isGlobal } = action.meta.arg;
            const { workbookId, file } = action.payload;
            const workbooks = isGlobal ? state.globalWorkbooks : state.userWorkbooks;
            const pendingState = isGlobal
              ? state.globalWorkbookPendingState
              : state.userWorkbookPendingState;
            workbooks[workbookId].files.push(file);
            pendingState[workbookId].filesPendingState[file.id] = {};
          },
        }
      ),
      callbackUploadFiles: create.asyncThunk(
        async ({
          workbookId,
          fileInfo,
          isGlobal,
        }: {
          workbookId: string;
          fileInfo: RetrievalFileSignedUrlUploadResponse[];
          isGlobal: boolean;
        }) => {
          const response = await postFilesCallback(workbookId, fileInfo, isGlobal);
          return response;
        },
        {
          fulfilled: (state, action) => {
            const { isGlobal } = action.meta.arg;
            const { workbookId, files } = action.payload;
            const workbooks = isGlobal ? state.globalWorkbooks : state.userWorkbooks;
            workbooks[workbookId].files = files;
          },
        }
      ),
      fetchFileIndexStatus: create.asyncThunk(
        async ({
          workbookId,
          fileId,
          isGlobal,
        }: {
          workbookId: string;
          fileId: string;
          isGlobal: boolean;
        }) => {
          const response = await getFileIndexStatus(workbookId, fileId, isGlobal);
          return response;
        },
        {
          fulfilled: (state, action) => {
            const { ready, file } = action.payload;
            const { workbookId, isGlobal } = action.meta.arg;
            if (ready && file) {
              const workbooks = isGlobal ? state.globalWorkbooks : state.userWorkbooks;
              const workbookFiles = workbooks[workbookId].files;
              const fileIndex = workbookFiles.findIndex(f => f.id === file.id);
              if (fileIndex != -1) {
                workbooks[workbookId].files.splice(fileIndex, 1, file);
              }
            }
          },
        }
      ),
      askMyWorkbookById: create.asyncThunk(
        async ({
          workbookId,
          workbookQuery,
        }: {
          workbookId: string;
          workbookQuery: RetrievalWorkbookQueryRequest;
        }) => {
          const response = await queryUserWorkbookById(workbookId, workbookQuery);
          return response;
        },
        {
          pending: (state, action) => {
            const { workbookId, workbookQuery } = action.meta.arg;
            const workbook = state.userWorkbooks[workbookId];
            if (workbook) {
              const session = workbook.sessions.find(
                session => session.id === workbookQuery?.session_id
              );
              if (session) {
                const now = new Date();
                const message: RetrievalSessionQuery = {
                  id: 'pending',
                  conversationRole: 'user',
                  query: workbookQuery.prompt,
                  createdUtc: now.toISOString(),
                };
                session.messages.push(message);
                state.userWorkbookPendingState[workbookId].sessionsPendingState[
                  session.id
                ].isQuerying = true;
                state.workbookQueryInProgress = true;
              }
            }
          },
          fulfilled: (state, action) => {
            const { workbookId } = action.meta.arg;
            const {
              workbookSessionId,
              query: queryMessage,
              answer: answerMessage,
            } = action.payload;
            const workbook = state.userWorkbooks[workbookId];
            if (workbook) {
              const session = workbook.sessions.find(session => session.id === workbookSessionId);
              if (session) {
                const latestQueryIndex = session.messages.findIndex(m => m.id === 'pending');
                if (latestQueryIndex != -1) {
                  session.messages.splice(latestQueryIndex, 1, queryMessage);
                }

                session.messages.push(answerMessage);
                state.userWorkbookPendingState[workbookId].sessionsPendingState[
                  session.id
                ].isQuerying = false;
                state.workbookQueryInProgress = false;
              }
            }
          },
          rejected: (state, action) => {
            const { workbookId, workbookQuery } = action.meta.arg;
            const workbook = state.userWorkbooks[workbookId];
            if (workbook) {
              const session = workbook.sessions.find(
                session => session.id === workbookQuery?.session_id
              );
              if (session) {
                state.userWorkbookPendingState[workbookId].sessionsPendingState[
                  session.id
                ].isQuerying = false;
                state.workbookQueryInProgress = false;
              }
            }
          },
        }
      ),
      askGlobalWorkbookById: create.asyncThunk(
        async ({
          workbookId,
          workbookQuery,
        }: {
          workbookId: string;
          workbookQuery: RetrievalWorkbookQueryRequest;
        }) => {
          const response = await queryGlobalWorkbookById(workbookId, workbookQuery);
          return response;
        },
        {
          pending: (state, action) => {
            const { workbookId, workbookQuery } = action.meta.arg;
            const workbook = state.globalWorkbooks[workbookId];
            if (workbook) {
              const session = workbook.sessions.find(
                session => session.id === workbookQuery?.session_id
              );
              if (session) {
                const now = new Date();
                const message: RetrievalSessionQuery = {
                  id: 'pending',
                  conversationRole: 'user',
                  query: workbookQuery.prompt,
                  createdUtc: now.toISOString(),
                };
                session.messages.push(message);
                state.globalWorkbookPendingState[workbookId].sessionsPendingState[
                  session.id
                ].isQuerying = true;
                state.workbookQueryInProgress = true;
              }
            }
          },
          fulfilled: (state, action) => {
            const { workbookId } = action.meta.arg;
            const {
              workbookSessionId,
              query: queryMessage,
              answer: answerMessage,
            } = action.payload;
            const workbook = state.globalWorkbooks[workbookId];
            if (workbook) {
              const session = workbook.sessions.find(session => session.id === workbookSessionId);
              if (session) {
                const latestQueryIndex = session.messages.findIndex(m => m.id === 'pending');
                if (latestQueryIndex != -1) {
                  session.messages.splice(latestQueryIndex, 1, queryMessage);
                }

                session.messages.push(answerMessage);
                state.globalWorkbookPendingState[workbookId].sessionsPendingState[
                  session.id
                ].isQuerying = false;
                state.workbookQueryInProgress = false;
              }
            }
          },
          rejected: (state, action) => {
            const { workbookId, workbookQuery } = action.meta.arg;
            const workbook = state.globalWorkbooks[workbookId];
            if (workbook) {
              const session = workbook.sessions.find(
                session => session.id === workbookQuery?.session_id
              );
              if (session) {
                state.globalWorkbookPendingState[workbookId].sessionsPendingState[
                  session.id
                ].isQuerying = false;
                state.workbookQueryInProgress = false;
              }
            }
          },
        }
      ),
      updateMessageFeedback: create.asyncThunk(
        async (feedbackRequest: RetrievalWorkbookMessageFeedbackRequest) => {
          const { workbookId, sessionId, messageId, feedback, isGlobal } = feedbackRequest;
          const data = await sendFeedbackByMessageId(
            workbookId,
            sessionId,
            messageId,
            feedback,
            isGlobal
          );
          return data;
        },
        {
          pending: (state, action) => {
            const { workbookId, sessionId, messageId, feedback, isGlobal } = action.meta.arg;
            const workbooks = isGlobal ? state.globalWorkbooks : state.userWorkbooks;
            const session = (workbooks?.[workbookId]?.sessions ?? []).find(s => s.id === sessionId);
            if (session) {
              const message = session.messages.find(m => m.id === messageId);
              if (message) {
                (message as RetrievalSessionAnswer).feedback = feedback;
              }
            }
          },
          fulfilled: (state, action) => {
            const { workbookId, sessionId, messageId, isGlobal } = action.meta.arg;
            const feedback = action.payload.data.answer.feedback;

            const workbooks = isGlobal ? state.globalWorkbooks : state.userWorkbooks;
            const session = (workbooks?.[workbookId]?.sessions ?? []).find(s => s.id === sessionId);
            if (session) {
              const message = session.messages.find(m => m.id === messageId);
              if (message) {
                (message as RetrievalSessionAnswer).feedback = feedback;
              }
            }
          },
        }
      ),
    };
  },
  selectors: {
    selectAllUserWorkbooks: workbooksState => {
      return workbooksState.userWorkbooks;
    },
    selectAllGlobalWorkbooks: workbooksState => {
      return workbooksState.globalWorkbooks;
    },
    selectUserWorkbookById: (workbooksState, workbookId: string) => {
      return workbooksState.userWorkbooks[workbookId];
    },
    selectGlobalWorkbookById: (workbooksState, workbookId: string) => {
      return workbooksState.globalWorkbooks[workbookId];
    },
    selectUserWorkbookSession: (workbooksState, workbookId: string, sessionId: string) => {
      return workbooksState.userWorkbooks[workbookId].sessions.find(s => s.id === sessionId);
    },
    selectGlobalWorkbookSession: (workbooksState, workbookId: string, sessionId) => {
      return workbooksState.globalWorkbooks[workbookId].sessions.find(s => s.id === sessionId);
    },
    selectLatestWorkbookSession: (
      workbooksState,
      workbookId: string,
      isGlobal: boolean = false
    ) => {
      const workbooks = isGlobal ? workbooksState.globalWorkbooks : workbooksState.userWorkbooks;
      const workbook = workbooks[workbookId];
      let latestSession: RetrievalSession | null = null;
      if (workbook.sessions) {
        latestSession = workbook.sessions[0];
        for (const session of workbook.sessions) {
          if (
            session.createdUtc &&
            (!latestSession.createdUtc || session.createdUtc > latestSession.createdUtc)
          ) {
            latestSession = session;
          }
        }
      }
      return latestSession;
    },
    selectWorkbookQueryInProgress: workbooksState => {
      return workbooksState.workbookQueryInProgress;
    },
    selectUserWorkbookPendingStatus: (workbooksState, workbookId: string) => {
      return workbooksState.userWorkbookPendingState[workbookId];
    },
    selectUserWorkbookFilePendingStatus: (workbooksState, workbookId: string, fileId) => {
      return workbooksState.userWorkbookPendingState[workbookId].filesPendingState[fileId];
    },
    selectUserWorkbookSessionPendingStatus: (
      workbooksState,
      workbookId: string,
      sessionId: string
    ) => {
      return workbooksState.userWorkbookPendingState[workbookId]?.sessionsPendingState?.[sessionId];
    },
    selectGlobalWorkbookPendingStatus: (workbooksState, workbookId: string) => {
      return workbooksState.globalWorkbookPendingState[workbookId];
    },
    selectGlobalWorkbookFilePendingStatus: (workbooksState, workbookId: string, fileId) => {
      return workbooksState.globalWorkbookPendingState[workbookId].filesPendingState[fileId];
    },
    selectGlobalWorkbookSessionPendingStatus: (
      workbooksState,
      workbookId: string,
      sessionId: string
    ) => {
      return workbooksState.globalWorkbookPendingState[workbookId].sessionsPendingState[sessionId];
    },
    selectMockLoadingFiles: (workbooksState, workbookId: string) => {
      return workbooksState.userWorkbooksMockLoadingFiles[workbookId] ?? [];
    },
  },
});

export const {
  createMyWorkbook,
  createPublicWorkbook,
  createMyWorkbookSession,
  fetchMyWorkbooks,
  fetchGlobalWorkbooks,
  fetchMyWorkbookById,
  fetchGlobalWorkbookById,
  fetchFileIndexStatus,
  updateWorkbookById,
  removeWorkbookById,
  removeWorkbookFileById,
  addMockLoadingFile,
  removeMockLoadingFile,
  callbackUploadFile,
  callbackUploadFiles,
  askMyWorkbookById,
  askGlobalWorkbookById,
  updateMessageFeedback,
} = workbookSlice.actions;
export const {
  selectAllUserWorkbooks,
  selectAllGlobalWorkbooks,
  selectUserWorkbookById,
  selectGlobalWorkbookById,
  selectUserWorkbookSession,
  selectLatestWorkbookSession,
  selectGlobalWorkbookSession,
  selectWorkbookQueryInProgress,
  selectUserWorkbookPendingStatus,
  selectUserWorkbookFilePendingStatus,
  selectUserWorkbookSessionPendingStatus,
  selectGlobalWorkbookPendingStatus,
  selectGlobalWorkbookFilePendingStatus,
  selectGlobalWorkbookSessionPendingStatus,
  selectMockLoadingFiles,
} = workbookSlice.selectors;

export default workbookSlice.reducer;
