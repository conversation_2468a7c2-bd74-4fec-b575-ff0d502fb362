import React, { useState, useEffect } from 'react';
import { MdOutlineModeEdit } from 'react-icons/md';
import './EditWorkbookModal.scss';

interface EditWorkbookModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSaveWorkbook: (newName: string) => Promise<void>;
  currentName: string;
  isUpdating?: boolean;
}

const EditWorkbookModal: React.FC<EditWorkbookModalProps> = ({
  isOpen,
  onOpenChange,
  onSaveWorkbook,
  currentName,
  isUpdating = false,
}) => {
  const [workbookName, setWorkbookName] = useState(currentName);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset the input when modal opens
  useEffect(() => {
    if (isOpen) {
      setWorkbookName(currentName);
    }
  }, [isOpen, currentName]);

  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onOpenChange(false);
    }
  };

  const handleCancel = () => {
    setWorkbookName(currentName);
    onOpenChange(false);
  };

  const handleSave = async () => {
    if (!workbookName.trim() || workbookName.trim() === currentName) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSaveWorkbook(workbookName.trim());
      onOpenChange(false);
    } catch (error) {
      console.error('Error saving workbook name:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !isSubmitting && workbookName.trim() && workbookName.trim() !== currentName) {
      handleSave();
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  };

  const isButtonDisabled = !workbookName.trim() || workbookName.trim() === currentName || isSubmitting || isUpdating;

  if (!isOpen) return null;

  return (
    <div className="edit-workbook-modal-overlay" onClick={handleOverlayClick}>
      <div className="edit-workbook-modal">
        <div className="edit-workbook-modal__header">
          <h2 className="edit-workbook-modal__title">Edit Workbook</h2>
        </div>

        <div className="edit-workbook-modal__content">
          <div className="edit-workbook-modal__input-container">
            <input
              type="text"
              value={workbookName}
              onChange={e => setWorkbookName(e.target.value)}
              onKeyDown={handleKeyPress}
              className="edit-workbook-modal__input"
              placeholder="Enter workbook name"
              autoFocus
              disabled={isSubmitting || isUpdating}
            />
            <MdOutlineModeEdit className="edit-workbook-modal__input-icon" />
          </div>

          <div className="edit-workbook-modal__button-container">
            <button
              type="button"
              onClick={handleCancel}
              className="edit-workbook-modal__button edit-workbook-modal__cancel-button"
              disabled={isSubmitting || isUpdating}
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleSave}
              className="edit-workbook-modal__button edit-workbook-modal__save-button"
              disabled={isButtonDisabled}
            >
              {isSubmitting || isUpdating ? 'Saving...' : 'Save Workbook'}
            </button>
          </div>
        </div>

        <div className="edit-workbook-modal__footer-divider" />
        <div className="edit-workbook-modal__footer">Upload guidelines go here.</div>
      </div>
    </div>
  );
};

export default EditWorkbookModal;
