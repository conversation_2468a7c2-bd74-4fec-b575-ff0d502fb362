import type { WorkbookCardProps } from '@/types';
import React, { memo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
// import { LuFileText } from 'react-icons/lu';
// import { LuTrash } from 'react-icons/lu';
// import { CircleLoader } from 'react-spinners';
import {
  MdExpandMore,
  MdExpandLess,
  MdOutlineSubdirectoryArrowRight,
  MdOutlineHistory,
  MdOutlineEdit,
  MdOutlineDeleteForever,
} from 'react-icons/md';
import { IoMdSettings } from 'react-icons/io';
import { Menu } from '@base-ui-components/react/menu';

import DeleteWorkbookModal from './DeleteWorkbookModal';
import EditWorkbookModal from './EditWorkbookModal';

import { formatSidekickDate } from '@/utils/dateUtils';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { useThemeStyles } from '@hooks/useThemeStyles';
import { showToast } from '@/components/common/ToastNotification/ToastNotification';

import {
  removeWorkbookById,
  updateWorkbookById,
  selectUserWorkbookPendingStatus,
  selectGlobalWorkbookPendingStatus,
} from '@/store/slices/workbookSlice';

// import { selectCurrentUser } from '@/store/slices/authSlice';

import './workbookCard.scss';

const WorkbookCard: React.FC<WorkbookCardProps> = memo(({ workbook, navigateTo, isGlobal = false }) => {
  const { classes, colors } = useThemeStyles();
  // TODO: Future UI - These variables will be needed when we restore file section and delete functionality
  // const { numFiles } = workbook;
  const navigate = useNavigate();
  const [menuOpen, setMenuOpen] = useState(false);
  const [isManageButtonHovered, setIsManageButtonHovered] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  const dispatch = useAppDispatch();
  const selectPendingState = isGlobal ? selectGlobalWorkbookPendingStatus : selectUserWorkbookPendingStatus;
  const workbookPendingStatus = useAppSelector(state => selectPendingState(state, workbook?.id ?? ''));
  // const currentUser = useAppSelector(selectCurrentUser);
  // const userIsAuthor = workbook?.author == currentUser;

  // TODO: Future UI - These theme variables will be needed when we restore file section
  const themeVars = {
    '--background-color': classes.background,
    '--border-color': colors.border,
    '--text-color': classes.text,
    '--text-muted-color': classes.textMuted,
    '--disable-card-hover': isManageButtonHovered ? '1' : '0',
    // '--file-border-color': colors.fileBorder,
    // '--sidebar-background-color': colors.backgroundSidebar,
    // '--sidebar-text-color': colors.textSidebar,
  } as React.CSSProperties;

  const formattedDate = formatSidekickDate(new Date(workbook.updatedUtc));

  // TODO: Future UI - Delete functionality preserved for future use
  // const onDeleteClick = async (e: React.MouseEvent<SVGElement, MouseEvent>) => {
  //   e.preventDefault();
  //   await dispatch(removeWorkbookById({ workbookId: workbook.id!, isGlobal: isGlobal! }));
  // };

  const handleRunClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    navigate(`${navigateTo ?? workbook.id}`);
  };

  const handleMenuItemClick = (action: string) => {
    console.log(`${action} clicked for workbook:`, workbook.id);
    setMenuOpen(false);

    if (action === 'Delete') {
      setShowDeleteModal(true);
    } else if (action === 'Edit') {
      setShowEditModal(true);
    }
    // TODO: Implement actual functionality for other actions
  };

  const handleDeleteWorkbook = async () => {
    try {
      await dispatch(removeWorkbookById({ workbookId: workbook.id!, isGlobal: isGlobal }));
      setShowDeleteModal(false);
      showToast.error('Workbook Deleted', 'There are always more workbooks to be created, though.');
    } catch (error) {
      console.error('Error deleting workbook:', error);
      showToast.error('Delete Failed', 'Failed to delete workbook. Please try again.');
    }
  };

  const handleSaveWorkbookName = async (newName: string) => {
    try {
      await dispatch(
        updateWorkbookById({
          workbookUpdates: {
            id: workbook.id!,
            name: newName,
            description: workbook.description,
          },
          isGlobal: isGlobal,
        })
      );
      showToast.positive('Workbook Updated', `Workbook renamed to "${newName}"`);
    } catch (error) {
      console.error('Error updating workbook name:', error);
      showToast.error('Update Failed', 'Failed to update workbook name. Please try again.');
      throw error; // Re-throw to let the modal handle the error state
    }
  };

  const CardContent = (
    <>
      <div className="workbook-card__header">
        <h3 id={`workbook-title-${workbook.id}`} className="workbook-card__title">
          {workbook.name}
        </h3>
        <div className="workbook-card__footer-info">
          <p className="workbook-card__date">Last edited {formattedDate}</p>
          <div className="workbook-card__divider"></div>
        </div>
      </div>

      <button className="workbook-card__run-button" onClick={handleRunClick}>
        <MdOutlineSubdirectoryArrowRight size={24} />
        <span>Run</span>
      </button>

      <Menu.Root open={menuOpen} onOpenChange={setMenuOpen}>
        <Menu.Trigger
          className="workbook-card__manage-button"
          onMouseEnter={() => setIsManageButtonHovered(true)}
          onMouseLeave={() => setIsManageButtonHovered(false)}
        >
          <div className="workbook-card__manage-content">
            {menuOpen ? <MdExpandLess size={20} /> : <MdExpandMore size={20} />}
            <span>Manage</span>
          </div>
        </Menu.Trigger>
        <Menu.Portal>
          <Menu.Positioner side="top" align="start">
            <Menu.Popup className="workbook-card__manage-popup">
              <Menu.Item className="workbook-card__manage-item" onClick={() => handleMenuItemClick('History')}>
                <MdOutlineHistory className="workbook-card__manage-icon" />
                <span>History</span>
              </Menu.Item>
              {!isGlobal && (
                <>
                  <Menu.Item className="workbook-card__manage-item" onClick={() => handleMenuItemClick('Edit')}>
                    <MdOutlineEdit className="workbook-card__manage-icon" />
                    <span>Edit</span>
                  </Menu.Item>
                  <Menu.Item className="workbook-card__manage-item" onClick={() => handleMenuItemClick('Delete')}>
                    <MdOutlineDeleteForever className="workbook-card__manage-icon" />
                    <span>Delete</span>
                  </Menu.Item>
                </>
              )}
              <Menu.Item className="workbook-card__manage-item" onClick={() => handleMenuItemClick('Settings')}>
                <IoMdSettings className="workbook-card__manage-icon" />
                <span>Settings</span>
              </Menu.Item>
            </Menu.Popup>
          </Menu.Positioner>
        </Menu.Portal>
      </Menu.Root>

      {/* TODO: Future UI - File count and delete section preserved for future use */}
      {/* <div className="workbook_card-misc">
        <div className="workbook_card-fileCount workbook-card__file-section">
          <LuFileText className="workbook-card__file-icon" color={colors.fileIconColor} aria-hidden="true" />
          <span className="workbook-card__file-text">
            {numFiles} file{numFiles === 1 ? '' : '(s)'} active
          </span>
        </div>
        {userIsAuthor && (
          <button className="workbook_card-delete">
            <LuTrash
              className="workbook-card__file-icon"
              color={colors.fileIconColor}
              aria-hidden="true"
              onClick={e => onDeleteClick(e)}
            />
          </button>
        )}
      </div> */}
    </>
  );

  // TODO: Future UI - Delete spinner component preserved for future use
  // const WorkbookDeleteSpinner = (
  //   <div className="workbook-deleting">
  //     <div>
  //       <h3 id={`workbook-title-${workbook.id}`} className="workbook-card__title">
  //         {workbook.name}
  //       </h3>
  //     </div>
  //     <div className="workbook-deleting-spinner">
  //       <CircleLoader loading={true} color="red" size={150} />
  //     </div>
  //   </div>
  // );

  return (
    <>
      <div className="workbook-card" style={themeVars} role="article" aria-labelledby={`workbook-title-${workbook.id}`}>
        <div className="workbook-card__content">
          {/* TODO: Future UI - Conditional rendering for delete state preserved for future use */}
          {/* {(workbookPendingStatus?.isDeleting ?? false) ? WorkbookDeleteSpinner : CardContent} */}
          {CardContent}
        </div>
      </div>

      <DeleteWorkbookModal
        isOpen={showDeleteModal}
        onOpenChange={setShowDeleteModal}
        onDeleteWorkbook={handleDeleteWorkbook}
        workbookName={workbook.name}
        isDeleting={workbookPendingStatus?.isDeleting ?? false}
      />

      <EditWorkbookModal
        isOpen={showEditModal}
        onOpenChange={setShowEditModal}
        onSaveWorkbook={handleSaveWorkbookName}
        currentName={workbook.name}
        isUpdating={workbookPendingStatus?.isUpdating ?? false}
      />
    </>
  );
});

export default WorkbookCard;
