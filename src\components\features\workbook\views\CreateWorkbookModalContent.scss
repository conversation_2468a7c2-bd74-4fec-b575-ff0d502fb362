// CSS Variables for better maintainability
:root {
  --primary-color: #0066b1;
  --background-dark: rgba(0, 45, 79, 1);
  --background-darker: #002d4f;
  --border-color: rgba(109, 120, 127, 1);
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.8);
  --text-muted: rgba(255, 255, 255, 0.6);
  --modal-background: #003963; /* Changed to requested color */
  --border-radius-sm: 6px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --spacing-xs: 8px;
  --spacing-sm: 12px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
  --font-sofia: 'Sofia_Pro', sans-serif;
  --font-roboto: 'Roboto', sans-serif;
}

// Modal overlay
.create-workbook-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 14, 26, 0.9); /* Changed to requested color */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
  padding: 20px;
  box-sizing: border-box;
}

// Main container
.create-workbook {
  position: relative;
  width: min(800px, 85vw); // 响应式宽度：最大800px，小屏幕时为85%视口宽度
  height: auto;
  max-height: 90vh; // 增加到90vh，给内容更多空间
  display: flex;
  flex-direction: column;
  color: var(--text-primary);
  background: var(--modal-background);
  border-radius: 32px;
  overflow: hidden;
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.18);

  // 小屏幕笔记本优化 (1366px及以下) - 更多地缩减宽度
  @media (max-width: 1366px) {
    width: min(600px, 75vw); // 从700px减少到600px，从80vw减少到75vw
    max-height: 88vh; // 保持更多高度
    border-radius: 24px; // 稍微减少圆角
  }

  // 更小的屏幕 (1200px及以下) - 进一步缩减宽度
  @media (max-width: 1200px) {
    width: min(500px, 70vw); // 从600px减少到500px，从90vw减少到70vw
    max-height: 90vh; // 保持充足高度
    border-radius: 20px;
  }

  // 非常小的屏幕 (1024px及以下) - 大幅缩减宽度
  @media (max-width: 1024px) {
    width: min(450px, 65vw); // 更小的最大宽度，更小的视口百分比
    max-height: 92vh; // 保持更多高度空间
    border-radius: 16px;
  }

  // 极小屏幕 (768px及以下) - 新增断点
  @media (max-width: 768px) {
    width: min(400px, 60vw);
    max-height: 95vh;
    border-radius: 12px;
  }

  // 高度较小的屏幕 - 只在真正需要时才限制高度
  @media (max-height: 700px) {
    max-height: 85vh;
  }

  @media (max-height: 600px) {
    max-height: 90vh;
  }
}

// Close button
.create-workbook__close-btn {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  color: var(--text-primary);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  z-index: 10;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    opacity: 0.8;
  }

  &:focus-visible {
    outline: 2px solid var(--text-primary);
    outline-offset: 2px;
  }
}

// Content area with proper flex layout
.create-workbook__content {
  flex: 1;
  padding: var(--spacing-xl) var(--spacing-xxl) var(--spacing-md) var(--spacing-xxl);
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  // 小屏幕优化padding
  @media (max-width: 1366px) {
    padding: 28px 36px 16px 36px;
  }

  @media (max-width: 1200px) {
    padding: 24px 32px 16px 32px;
  }

  @media (max-width: 1024px) {
    padding: 20px 24px 12px 24px;
  }

  @media (max-height: 700px) {
    padding: 20px 28px 12px 28px;
  }
}

// Title styling
.create-workbook__title {
  font-family: var(--font-sofia);
  font-size: 48px;
  font-weight: 500;
  line-height: 130%;
  letter-spacing: 0.384px;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xl) 0;

  // 小屏幕字体优化
  @media (max-width: 1366px) {
    font-size: 42px;
    margin-bottom: 28px;
  }

  @media (max-width: 1200px) {
    font-size: 36px;
    margin-bottom: 24px;
  }

  @media (max-width: 1024px) {
    font-size: 32px;
    margin-bottom: 20px;
  }

  @media (max-height: 700px) {
    font-size: 32px;
    margin-bottom: 20px;
  }
}

// Reusable input group
.input-group {
  position: relative;
  width: 100%;
  margin-bottom: var(--spacing-md);
}

// Base input field styles
.input-field {
  width: 100%;
  background: var(--background-dark);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-primary);
  font-family: var(--font-roboto);
  font-size: 16px;
  outline: none;
  transition: border-color 0.2s ease;

  &::placeholder {
    color: var(--text-muted);
  }

  &:focus {
    border-color: var(--primary-color);
  }

  // Input with icon
  &--with-icon {
    padding-right: 48px;
  }
}

// Input field icon
.input-field__icon {
  position: absolute;
  right: var(--spacing-sm);
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  transition: color 0.2s ease;

  &--inactive {
    color: var(--text-muted);
  }

  &--active {
    color: var(--text-primary);
  }
}

// Section styling
section {
  display: flex;
  flex-direction: column;
  margin-bottom: var(--spacing-md); /* Ensures 16px gap between sections */
  
  @media (max-height: 700px) {
    margin-bottom: 12px;
  }
}

// Section titles
.section-title {
  font-family: var(--font-sofia);
  font-size: 16px;
  font-weight: 700;
  line-height: 130%;
  letter-spacing: 0.128px;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  height: 30px;
  display: flex;
  align-items: center;
}

// System instructions section
.system-instructions {
  .section-title {
    margin-bottom: 0;
  }
  .section-description {
    width: 450px;
    font-family: var(--font-roboto);
    font-size: 12px;
    font-weight: 400;
    line-height: 120%;
    color: var(--text-secondary);
    margin: 0 0 var(--spacing-xs) 0;
    
    @media (max-width: 900px) {
      width: 100%;
      max-width: 450px;
    }
  }
}

// Textarea group (similar to input-group)
.textarea-group {
  position: relative;
  width: 100%;
}

// Textarea field
.textarea-field {
  width: 100%;
  height: 144px;
  background: var(--background-dark);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-xs) var(--spacing-lg) var(--spacing-lg) var(--spacing-lg);
  color: var(--text-primary);
  font-family: var(--font-roboto);
  font-size: 14px;
  outline: none;
  resize: none;
  transition: border-color 0.2s ease;

  &::placeholder {
    color: var(--text-muted);
  }

  &:focus {
    border-color: var(--primary-color);
  }
  
  @media (max-height: 700px) {
    height: 100px;
  }

  // Textarea with icon
  &--with-icon {
    padding-right: 48px;
  }
}

// Textarea field icon
.textarea-field__icon {
  position: absolute;
  right: var(--spacing-sm);
  top: var(--spacing-sm);
  pointer-events: none;
  transition: color 0.2s ease;

  &--inactive {
    color: var(--text-muted);
  }

  &--active {
    color: var(--text-primary);
  }
}

// Chunk size section
.chunk-size-section {
  margin-bottom: 16px;
  .section-title {
    font-weight: 500;
    margin-bottom: var(--spacing-sm);
  }
}

// Radio group
.chunk-size-radio-group {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: var(--spacing-lg);
  height: 44px;
  padding: 4px;

  &__option {
    display: flex;
    align-items: center;
    gap: 4px;
    color: var(--text-primary);
    font-family: var(--font-roboto);
    font-size: 14px;
    font-weight: 400;
    line-height: 160%;
    letter-spacing: 0.112px;
    cursor: pointer;
  }
  
  @media (max-width: 900px) {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
}

// Radio item styling
.radio-item {
  display: flex;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  aspect-ratio: 1/1;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 2px solid #ffffff; /* Changed to white border */
  background-color: transparent;
  transition: background-color 0.2s ease;

  &[data-checked] {
    background-color: transparent;
  }

  &:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }
}

// Radio indicator
.radio-indicator {
  display: flex;
  align-items: center;
  justify-content: center;

  &[data-unchecked] {
    display: none;
  }

  &::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--text-primary);
  }
}

// File upload section
.file-upload-section {
  margin-bottom: var(--spacing-xl);
  
  @media (max-height: 700px) {
    margin-bottom: 20px;
  }
}

// File upload button
.file-upload-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  width: 110px;
  height: 45px;
  padding: var(--spacing-sm);
  background: var(--primary-color);
  border: none;
  border-radius: 4px;
  color: var(--text-primary);
  font-family: var(--font-roboto);
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #0052a3;
  }
}

// File list
.file-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
}

// Individual file item
.file-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--background-dark);
  border: 1px solid var(--primary-color);
  border-radius: var(--border-radius-sm);
  color: var(--text-primary);
  font-family: var(--font-roboto);
  font-size: 12px;
  min-width: 0;

  &__icon {
    flex-shrink: 0;
    margin-right: var(--spacing-xs);
    display: flex;
    align-items: center;
  }

  &__name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: var(--spacing-sm);
    min-width: 0;
  }

  &__delete-btn {
    flex-shrink: 0;
    background: none;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    transition: color 0.2s ease;

    &:hover {
      color: #ff6b6b;
    }

    &:focus-visible {
      outline: 2px solid var(--primary-color);
      outline-offset: 2px;
      border-radius: 2px;
    }
  }
}

// Form actions
.form-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-top: auto;
  /* Removed padding-top as requested */
  
  @media (max-height: 700px) {
    /* Removed padding-top here too */
  }
}

// Button base styles
.btn {
  display: flex;
  align-items: center;
  font-family: var(--font-roboto);
  font-size: 16px;
  cursor: pointer;
  border: none;
  transition: all 0.3s ease;
  outline: none;

  &:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }

  // Secondary button (Cancel)
  &--secondary {
    background: transparent;
    color: var(--text-primary);
    padding: var(--spacing-xs) var(--spacing-md);
    /* Removed text-decoration: underline */

    &:hover {
      opacity: 0.8;
    }
  }

  // Primary button (Create)
  &--primary {
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    background: transparent;
    border: 2px solid var(--primary-color);
    border-radius: 50px;
    color: var(--text-primary);

    &:hover:not(.btn--disabled) {
      background-color: var(--primary-color);
    }

    &.btn--disabled {
      background: transparent;
      border: 2px solid var(--primary-color);
      color: var(--text-primary);
      cursor: not-allowed;
      opacity: 0.5;

      &:hover {
        background-color: transparent;
      }
    }
  }
}

// Footer
.create-workbook__footer {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-md) var(--spacing-xxl);
  border-top: 1px solid rgba(0, 102, 177, 0.3);
  color: var(--text-secondary);
  font-family: var(--font-roboto);
  font-size: 12px;
  font-weight: 400;
  width: 100%;
  flex-shrink: 0;

  // 小屏幕footer优化
  @media (max-width: 1366px) {
    padding: 14px 36px;
  }

  @media (max-width: 1200px) {
    padding: 12px 32px;
  }

  @media (max-width: 1024px) {
    padding: 10px 24px;
    font-size: 11px;
  }

  @media (max-height: 700px) {
    padding: 10px 28px;
  }
}
