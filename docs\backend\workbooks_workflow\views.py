import traceback
from datetime import datetime, timedelta, timezone
from typing import List

import google.cloud.exceptions as GCPExceptions
from flask import Blueprint, Request, Response, jsonify, make_response, request, session
from pydantic_core import ValidationError
from website.logger_framework import make_logger

from models.retrieval_workbook.RetrievalEnums import RetrievalWorkbookChunkSize
from models.retrieval_workbook.RetrievalWorkbook import RetrievalWorkbook, UpdateRetrievalWorkbook
from models.retrieval_workbook.RetrievalFile import RetrievalFileSignedUrlRequest, RetrievalFileSignedUrl, CreateRetrievalFile, UpdateRetrievalFile
from .response_models import CreateRetrievalWorkbookResponse, GetRetrievalWorkbooksResponse, UpdateRetrievalWorkbookResponse

from services.RetrievalWorkbookService import RetrievalWorkbookService
from services.RetrievalFileService import RetrievalFileService

from website.authorization import admin_filter
from website.logger_framework import make_logger
from lib.firestore_client import FirestoreClient
from models.retrieval_workbook.RetrievalCitation import RetrievalCitation, RetrievalCitationSource
from models.retrieval_workbook.RetrievalSession import CreateRetrievalSessionAnswer, CreateRetrievalSessionQuery, RetrievalSession, CreateRetrievalSession
from models.retrieval_workbook.RetrievalEnums import RetrievalWorkbookChunkSize
from models.retrieval_workbook.RetrievalFile import (
    CreateRetrievalFile, RetrievalFileSignedUrl, RetrievalFileSignedUrlRequest)
from models.retrieval_workbook.RetrievalWorkbook import UpdateRetrievalWorkbook
from services.RetrievalFileService import RetrievalFileService
from services.RetrievalSessionService import RetrievalSessionService
from services.RetrievalWorkbookService import RetrievalWorkbookService
from services.VertexAISearchService import VertexAISearchService

from .response_models import (CreateRetrievalWorkbookResponse,
                              GetRetrievalWorkbooksResponse,
                              UpdateRetrievalWorkbookResponse)
from website.utils.db_utils import add_workbook_message

workflow = "workbooks_workflow"

logger_info, logger_error = make_logger(workflow, __file__)

workbooks = Blueprint("workbooks_process", __name__)


@workbooks.errorhandler(ValidationError)
def handle_pydantic_validation_error(error: ValidationError):
    logger_error.error(
        f"{workflow} : model validation : {request.method} {request.path} : {str(error)} : {traceback.format_exc()}"
    )
    return make_response(
        jsonify({
            "error": error.json(),
            "reason": "validation"
        }), 422
    )


@workbooks.errorhandler(GCPExceptions.NotFound)
def handle_gcp_not_found(error: GCPExceptions.NotFound):
    logger_error.error(
        f"{workflow} : gcp not found: {request.method} {request.path} : {str(error)} : {traceback.format_exc()}"
    )
    return make_response(
        jsonify({
            "error": error.message,
            "reason": "not found"
        }), 404
    )


@workbooks.errorhandler(GCPExceptions.Conflict)
def handle_gcp_conflict(error: GCPExceptions.Conflict):
    logger_error.error(
        f"{workflow} : gcp conflict : {request.method} {request.path} : {str(error)} : {traceback.format_exc()}"
    )
    return make_response(
        jsonify({
            "error": error.message,
            "reason": "conflict"
        }), 409
    )


@workbooks.errorhandler(Exception)
def handle_unexpected_error(error: Exception):
    logger_error.error(
        f"{workflow} : unexpected : {request.method} {request.path} : {str(error)} : {traceback.format_exc()}"
    )
    return make_response(
        jsonify({
            "error": str(error),
            "reason": "unexpected"
        }), 500
    )


def _create_retrieval_workbook(request: Request, is_global: bool = False) -> Response:
    request_body: dict = request.get_json()
    request_user = session.get("email")
    workbook_name = request_body.get("name")
    workbook_description = request_body.get("description", None)
    workbook_chunk_size = request_body.get(
        "chunkSize", RetrievalWorkbookChunkSize.large)
    if not isinstance(workbook_chunk_size, RetrievalWorkbookChunkSize):
        try:
            workbook_chunk_size = RetrievalWorkbookChunkSize(
                workbook_chunk_size)
        except Exception:
            workbook_chunk_size = RetrievalWorkbookChunkSize.large

    workbook_client = None
    workbook_service = None
    existing_workbooks = None
    if is_global:
        workbook_client = FirestoreClient().GlobalInstanceClient()
        workbook_service = RetrievalWorkbookService(
            persistence_client=workbook_client, global_persistence_client=workbook_client)
        existing_workbooks = workbook_service.get_workbooks_by_name(
            workbook_name=workbook_name)
    else:
        workbook_client = FirestoreClient().InstanceClient(user_email=request_user)
        workbook_service = RetrievalWorkbookService(
            persistence_client=workbook_client)
        existing_workbooks = workbook_service.get_workbooks_by_name(
            workbook_name=workbook_name, workbook_author=request_user)

    if existing_workbooks:
        raise GCPExceptions.Conflict(
            f'Workbook with name "{workbook_name}" already exists')

    created_workbook = workbook_service.create_workbook(
        workbook_name=workbook_name,
        author_email=request_user,
        description=workbook_description,
        chunk_size=workbook_chunk_size,
    )

    response_body = {
        "workbook": created_workbook.to_dict(date_format_iso=True, include_document_id=True, to_camel=True)
    }

    return make_response(response_body, 200)


@workbooks.post("/my")
def create_retrieval_workbook():
    return _create_retrieval_workbook(request, is_global=False)


@workbooks.post("/public")
@admin_filter
def create_global_retrieval_workbook():
    return _create_retrieval_workbook(request, is_global=True)


@workbooks.get("/my")
def get_retrieval_workbooks_for_user():
    user = session.get('email')
    workbook_client = FirestoreClient().InstanceClient(user_email=user)
    workbook_service = RetrievalWorkbookService(
        persistence_client=workbook_client)

    workbook_documents = workbook_service.get_workbooks_by_author(user_id=user)

    response_body = GetRetrievalWorkbooksResponse(
        user=user,
        workbooks=workbook_documents
    )

    return make_response(response_body.to_dict(date_format_iso=True, include_document_id=True, to_camel=True), 200)


@workbooks.get("/public")
def get_global_workbooks():
    user = session.get("email")
    workbook_client = FirestoreClient().InstanceClient(user_email=user)
    workbook_service = RetrievalWorkbookService(
        persistence_client=workbook_client)

    global_workbooks = workbook_service.get_global_workbooks()
    response_body = GetRetrievalWorkbooksResponse(
        user=None,
        workbooks=global_workbooks
    )

    return make_response(response_body.to_dict(date_format_iso=True, include_document_id=True, to_camel=True), 200)


@workbooks.get("/my/<workbook_id>")
def get_user_retrieval_workbook_by_id(workbook_id: str):
    user = session.get('email')
    workbook_client = FirestoreClient().InstanceClient(user_email=user)
    workbook_service = RetrievalWorkbookService(
        persistence_client=workbook_client)

    workbook_document = workbook_service.get_authorized_workbook_by_id(
        id=workbook_id,
        user_email=user,
        with_files=True,
        with_sessions=True,
        is_global=False,
    )
    if not workbook_document:
        return make_response(
            {"error": f"No workbook with id: {workbook_id} could be found"}, 404
        )
    else:
        response_body = workbook_document.to_dict(
            date_format_iso=True, include_document_id=True, to_camel=True)
        return make_response(response_body, 200)


@workbooks.get("/public/<workbook_id>")
def get_global_retrieval_workbook_by_id(workbook_id: str):
    user = session.get('email')
    workbook_client = FirestoreClient().GlobalInstanceClient()
    workbook_service = RetrievalWorkbookService(
        persistence_client=workbook_client, global_persistence_client=workbook_client)

    workbook_document = workbook_service.get_workbook_by_id(
        id=workbook_id,
        with_files=True,
        with_sessions=True,
        user_email=user,
        is_global=True
    )
    if not workbook_document:
        return make_response(
            {"error": f"No workbook with id: {workbook_id} could be found"}, 404
        )
    else:
        response_body = workbook_document.to_dict(
            date_format_iso=True, include_document_id=True, to_camel=True)
        return make_response(response_body, 200)


@workbooks.patch("/my/<workbook_id>")
def update_retrieval_workbook(workbook_id: str):
    request_body: dict = request.get_json()
    request_user = session.get("email")
    workbook_name = request_body.get("name")
    workbook_description = request_body.get("description", None)
    clear_description = request_body.get("clear_description", False)

    updates = UpdateRetrievalWorkbook(
        name=workbook_name,
        description=workbook_description,
        clear_description=clear_description
    )

    workbook_client = FirestoreClient().InstanceClient(user_email=request_user)
    workbook_service = RetrievalWorkbookService(
        persistence_client=workbook_client)

    updated_at: datetime = workbook_service.update_workbook(
        workbook_id=workbook_id,
        workbook_updates=updates
    )

    response_body = {
        "id": workbook_id,
        "name": workbook_name,
        "updatedUTC": updated_at.isoformat()
    }
    if workbook_description is not None:
        response_body["description"] = workbook_description
    if clear_description:
        response_body["description"] = None
    response_body = UpdateRetrievalWorkbookResponse(
        id=workbook_id,
        name=workbook_name,
        updated_utc=updated_at
    )
    if workbook_description is not None:
        response_body.description = workbook_description
    if clear_description:
        response_body.description = None

    return make_response(response_body.to_dict(date_format_iso=True, include_document_id=True, to_camel=True), 200)


@workbooks.patch("/public/<workbook_id>")
def update_global_retrieval_workbook(workbook_id: str):
    request_body: dict = request.get_json()
    request_user = session.get("email")
    workbook_name = request_body.get("name")
    workbook_description = request_body.get("description", None)
    clear_description = request_body.get("clear_description", False)

    updates = UpdateRetrievalWorkbook(
        name=workbook_name,
        description=workbook_description,
        clear_description=clear_description
    )

    workbook_client = FirestoreClient().GlobalInstanceClient()
    workbook_service = RetrievalWorkbookService(
        persistence_client=workbook_client)

    workbook = workbook_service.get_workbook_by_id(workbook_id)
    updated_at: datetime = datetime.now(timezone.utc)
    if request_user and workbook.author == request_user.strip().lower():
        updated_at: datetime = workbook_service.update_workbook(
            workbook_id=workbook_id,
            workbook_updates=updates
        )

    response_body = {
        "id": workbook_id,
        "name": workbook_name,
        "updatedUTC": updated_at.isoformat()
    }
    if workbook_description is not None:
        response_body["description"] = workbook_description
    if clear_description:
        response_body["description"] = None
    response_body = UpdateRetrievalWorkbookResponse(
        id=workbook_id,
        name=workbook_name,
        updated_utc=updated_at
    )
    if workbook_description is not None:
        response_body.description = workbook_description
    if clear_description:
        response_body.description = None

    return make_response(response_body.to_dict(date_format_iso=True, include_document_id=True, to_camel=True), 200)


@workbooks.delete("/my/<workbook_id>")
def delete_retrieval_workbook_by_id(workbook_id: str):
    user = session.get('email')
    workbook_client = FirestoreClient().InstanceClient(user_email=user)
    workbook_service = RetrievalWorkbookService(
        persistence_client=workbook_client)

    workbook_service.delete_workbook(user_email=user, workbook_id=workbook_id)
    response_body = {
        "id": workbook_id,
    }

    return make_response(jsonify(response_body), 200)


@workbooks.delete("/public/<workbook_id>")
@admin_filter
def delete_global_retrieval_workbook_by_id(workbook_id: str):
    user = session.get('email')
    workbook_client = FirestoreClient().GlobalInstanceClient()
    workbook_service = RetrievalWorkbookService(
        persistence_client=workbook_client, global_persistence_client=workbook_client)

    workbook_service.delete_workbook(
        user_email=user, workbook_id=workbook_id, is_global=True)
    response_body = {
        "id": workbook_id,
    }

    return make_response(jsonify(response_body), 200)


@workbooks.delete("/my/<workbook_id>/files/<file_id>")
def delete_retrieval_file(workbook_id: str, file_id: str):
    user: str = session.get('email', "")
    workbook_client = FirestoreClient().InstanceClient(user_email=user)
    workbook_service = RetrievalWorkbookService(
        persistence_client=workbook_client)
    workbook_file_service = RetrievalFileService(
        persistence_client=workbook_client)
    workbook: RetrievalWorkbook | None = workbook_service.get_authorized_workbook_by_id(
        id=workbook_id,
        user_email=user,
        is_global=False
    )
    if user and workbook and user.strip().lower() == workbook.author:
        vais_service = VertexAISearchService(
            domain=user.strip().lower().split("@")[1],
            chunk_size=workbook.chunk_size
        )
        workbook_file = workbook_file_service.get_file_by_id(
            workbook_id, file_id)
        workbook_file_service.delete_workbook_file_and_artifacts(
            user_email=user,
            workbook_id=workbook_id,
            file=workbook_file,
            vais_service=vais_service
        )
        return make_response({
            "workbookId": workbook_id,
            "fileId": file_id
        }, 200)
    else:
        return make_response({
            "error": f"Workbook with id: {workbook_id} could not be found for user: {user}",
            "reason": "not found"
        }, 404)


@workbooks.delete("/public/<workbook_id>/files/<file_id>")
@admin_filter
def delete_global_retrieval_file(workbook_id: str, file_id: str):
    user: str = session.get('email', "")
    workbook_client = FirestoreClient().GlobalInstanceClient()
    workbook_service = RetrievalWorkbookService(
        persistence_client=workbook_client, global_persistence_client=workbook_client)
    workbook_file_service = RetrievalFileService(
        persistence_client=workbook_client, global_persistence_client=workbook_client)
    workbook: RetrievalWorkbook | None = workbook_service.get_authorized_workbook_by_id(
        id=workbook_id,
        user_email=user,
        is_global=True
    )
    if user and workbook and user.strip().lower() in workbook.author:
        vais_service = VertexAISearchService(
            domain="default",
            chunk_size=workbook.chunk_size
        )
        workbook_file = workbook_file_service.get_file_by_id(
            workbook_id, file_id)
        workbook_file_service.delete_workbook_file_and_artifacts(
            user_email=user,
            workbook_id=workbook_id,
            file=workbook_file,
            vais_service=vais_service,
            is_global=True
        )
        return make_response({
            "workbookId": workbook_id,
            "fileId": file_id
        }, 200)
    else:
        return make_response({
            "error": f"Workbook with id: {workbook_id} could not be found for user: {user}",
            "reason": "not found"
        }, 404)


@workbooks.post("/my/<workbook_id>/files/sign")
def create_signed_urls_for_files(workbook_id):
    request_body: dict = request.get_json()
    request_user = session.get("email")
    files_to_sign = request_body.get("filesToSign", [])
    signed_url_requests = [
        RetrievalFileSignedUrlRequest(
            file_name=file_request["name"],
            mime_type=file_request["mimeType"],
            file_size_bytes=file_request["fileSizeBytes"]
        ) for file_request in files_to_sign
    ]

    client = FirestoreClient().InstanceClient(user_email=request_user)
    file_service = RetrievalFileService(persistence_client=client)
    workbook_signed_urls: List[RetrievalFileSignedUrl] = []

    workbook_signed_urls: List[RetrievalFileSignedUrl] = file_service.create_upload_signed_urls(
        user_email=request_user,
        workbook_id=workbook_id,
        signed_upload_requests=signed_url_requests
    )

    response_body = {
        "filesSignedUrls": [signed_url.to_dict(to_camel=True) for signed_url in workbook_signed_urls]
    }
    return make_response(jsonify(response_body), 200)


@workbooks.post("/public/<workbook_id>/files/sign")
@admin_filter
def create_signed_urls_for_global_files(workbook_id):
    request_body: dict = request.get_json()
    request_user = session.get("email")
    files_to_sign = request_body.get("filesToSign", [])
    signed_url_requests = [
        RetrievalFileSignedUrlRequest(
            file_name=file_request["name"],
            mime_type=file_request["mimeType"],
            file_size_bytes=file_request["fileSizeBytes"]
        ) for file_request in files_to_sign
    ]

    client = FirestoreClient().GlobalInstanceClient()
    file_service = RetrievalFileService(
        persistence_client=client, global_persistence_client=client)
    workbook_signed_urls: List[RetrievalFileSignedUrl] = []

    workbook_signed_urls: List[RetrievalFileSignedUrl] = file_service.create_upload_signed_urls(
        user_email=request_user,
        workbook_id=workbook_id,
        signed_upload_requests=signed_url_requests,
        is_global=True
    )

    response_body = {
        "filesSignedUrls": [signed_url.to_dict(to_camel=True) for signed_url in workbook_signed_urls]
    }
    return make_response(jsonify(response_body), 200)


@workbooks.get("/my/<workbook_id>/file/<file_id>/index_status")
def workbook_file_index_status(workbook_id, file_id):
    request_user = session.get("email")

    persistence_client = FirestoreClient().InstanceClient(user_email=request_user)
    workbook_service = RetrievalWorkbookService(
        persistence_client=persistence_client)
    workbook_file_service = RetrievalFileService(
        persistence_client=persistence_client)

    workbook = workbook_service.get_authorized_workbook_by_id(
        id=workbook_id, user_email=request_user)
    workbook_file = None
    vais_document_is_indexed = workbook_service.get_vais_document_index_status(
        workbook, file_id)
    if vais_document_is_indexed:
        workbook_file_service.update_file(
            workbook_id=workbook.id,
            file_id=file_id,
            file_updates=UpdateRetrievalFile(is_chunked=True)
        )
        workbook_file = workbook_service.get_workbook_file(
            workbook_id, file_id)

    response_body = {
        "ready": vais_document_is_indexed,
        "file": workbook_file.to_dict(date_format_iso=True, include_document_id=True, to_camel=True) if workbook_file else None
    }
    return make_response(response_body, 200)


@workbooks.get("/public/<workbook_id>/file/<file_id>/index_status")
def global_workbook_file_index_status(workbook_id, file_id):
    request_user = session.get("email")

    persistence_client = FirestoreClient().GlobalInstanceClient()
    workbook_service = RetrievalWorkbookService(
        persistence_client=persistence_client, global_persistence_client=persistence_client)
    workbook_file_service = RetrievalFileService(
        persistence_client=persistence_client, global_persistence_client=persistence_client)

    workbook = workbook_service.get_authorized_workbook_by_id(
        id=workbook_id, user_email=request_user, is_global=True)
    workbook_file = None
    vais_document_is_indexed = workbook_service.get_vais_document_index_status(
        workbook, file_id, is_global=True)
    if vais_document_is_indexed:
        workbook_file_service.update_file(
            workbook_id=workbook.id,
            file_id=file_id,
            file_updates=UpdateRetrievalFile(is_chunked=True)
        )
        workbook_file = workbook_service.get_workbook_file(
            workbook_id, file_id)

    response_body = {
        "ready": vais_document_is_indexed,
        "file": workbook_file.to_dict(date_format_iso=True, include_document_id=True, to_camel=True) if workbook_file else None
    }
    return make_response(response_body, 200)


@workbooks.put("/my/<workbook_id>/file/<file_name>/uploaded")
def workbook_file_uploaded_callback(workbook_id, file_name):
    request_body: dict = request.get_json()
    request_user = session.get("email")
    uploaded_file = request_body.get("uploadedFile")
    create_retrieval_file = CreateRetrievalFile(
        name=uploaded_file.get("name"),
        display_name=uploaded_file.get("name"),
        mime_type=uploaded_file.get("mimeType"),
        file_size_bytes=uploaded_file.get("fileSizeBytes"),
        gcs_path=uploaded_file.get("gcsPath")
    )

    workbook_client = FirestoreClient().InstanceClient(user_email=request_user)
    workbook_service = RetrievalWorkbookService(
        persistence_client=workbook_client)

    workbook_file = workbook_service.callback_upload_file(
        request_user, workbook_id, create_retrieval_file)
    if workbook_file:
        response_body = {
            "workbookId": workbook_id,
            "file": workbook_file.to_dict(date_format_iso=True, include_document_id=True, to_camel=True)
        }
        return make_response(response_body, 200)
    else:
        return make_response(
            jsonify({
                "error": f"{request_user} is unauthorized to modify {workbook_id}",
                "reason": "unauthorized"
            }), 401
        )


@workbooks.put("/public/<workbook_id>/file/<file_name>/uploaded")
@admin_filter
def global_workbook_file_uploaded_callback(workbook_id, file_name):
    request_body: dict = request.get_json()
    request_user = session.get("email")
    uploaded_file = request_body.get("uploadedFile")
    create_retrieval_file = CreateRetrievalFile(
        name=uploaded_file.get("name"),
        display_name=uploaded_file.get("name"),
        mime_type=uploaded_file.get("mimeType"),
        file_size_bytes=uploaded_file.get("fileSizeBytes"),
        gcs_path=uploaded_file.get("gcsPath")
    )

    workbook_client = FirestoreClient().GlobalInstanceClient()
    workbook_service = RetrievalWorkbookService(
        persistence_client=workbook_client, global_persistence_client=workbook_client)

    workbook_file = workbook_service.callback_upload_file(
        request_user, workbook_id, create_retrieval_file, is_global=True)
    if workbook_file:
        response_body = {
            "workbookId": workbook_id,
            "file": workbook_file.to_dict(date_format_iso=True, include_document_id=True, to_camel=True)
        }
        return make_response(response_body, 200)
    else:
        return make_response(
            jsonify({
                "error": f"{request_user} is unauthorized to modify {workbook_id}",
                "reason": "unauthorized"
            }), 401
        )


@workbooks.post("/my/<workbook_id>/files/uploaded")
def workbook_files_uploaded_callback(workbook_id):
    request_body: dict = request.get_json()
    request_user = session.get("email")
    uploaded_files: list = request_body.get("uploadedFiles", [])
    create_retrieval_files = [
        CreateRetrievalFile(
            name=uploaded_file.get("name"),
            display_name=uploaded_file.get("name"),
            mime_type=uploaded_file.get("mimeType"),
            file_size_bytes=uploaded_file.get("fileSizeBytes"),
            gcs_path=uploaded_file.get("gcsPath"),
        ) for uploaded_file in uploaded_files
    ]

    workbook_client = FirestoreClient().InstanceClient(user_email=request_user)
    workbook_service = RetrievalWorkbookService(
        persistence_client=workbook_client)

    workbook_files = workbook_service.callback_upload_files(
        request_user, workbook_id, create_retrieval_files)
    response_body = {
        "workbookId": workbook_id,
        "files": [workbook_file.to_dict(date_format_iso=True, include_document_id=True, to_camel=True) for workbook_file in workbook_files]
    }
    return make_response(response_body, 200)


@workbooks.post("/my/<workbook_id>/ask")
def query_user_workbook(workbook_id):
    request_body: dict = request.get_json()
    prompt: str = request_body.get("prompt")
    session_id: str | None = request_body.get("session_id")
    request_user: str = session.get("email")

    if not prompt:
        return make_response(
            {"error": f"No prompt provided"}, 400
        )

    domain = request_user.split("@")[1]
    workbook_client = FirestoreClient().InstanceClient(user_email=request_user)
    workbook_service = RetrievalWorkbookService(
        persistence_client=workbook_client)
    session_service = RetrievalSessionService(
        persistence_client=workbook_client)
    workbook_document = workbook_service.get_authorized_workbook_by_id(
        id=workbook_id,
        with_files=True,
        with_sessions=True,
        user_email=request_user,
        is_global=False
    )
    if not workbook_document:
        return make_response(
            {"error": f"No workbook with id: {workbook_id} could be found"}, 404
        )

    new_session = True
    if not session_id:
        workbook_sessions = session_service.get_user_sessions_by_workbook_id(
            user_email=request_user,
            workbook_id=workbook_id
        )
        workbook_session = workbook_sessions[0] if workbook_sessions else session_service.create_workbook_session(
            workbook_id, request_user, [])
    else:
        new_session = False
        workbook_session = session_service.get_session_by_id(
            workbook_id, session_id)

    message = CreateRetrievalSessionQuery(query=prompt)

    created_message = session_service.add_message(
        workbook_id, workbook_session.id, message
    )

    pubsub_message = RetrievalSessionService.to_pubsub_message(
        workbook_user=request_user,
        workbook_id=workbook_document.id,
        session_id=workbook_session.id,
        message=created_message,
    )
    add_workbook_message(pubsub_message)

    # split here for async

    created_answer = session_service.ask_message(
        workbook_id, prompt, domain, workbook_document.chunk_size, workbook_session.id, new_session)

    pubsub_message = RetrievalSessionService.to_pubsub_message(
        workbook_user=request_user,
        workbook_id=workbook_document.id,
        session_id=workbook_session.id,
        message=created_answer,
    )
    add_workbook_message(pubsub_message)

    response = make_response(jsonify({
        "workbookSessionId": workbook_session.id,
        "query": created_message.to_dict(True, True, True),
        "answer": created_answer.to_dict(True, True, True)
    }))

    return response


@workbooks.post("/public/<workbook_id>/ask")
def query_global_workbook(workbook_id):
    request_body: dict = request.get_json()
    prompt: str = request_body.get("prompt")
    session_id: str | None = request_body.get("session_id")
    request_user: str = session.get("email")

    if not prompt:
        return make_response(
            {"error": f"No prompt provided"}, 400
        )

    workbook_client = FirestoreClient().GlobalInstanceClient()
    workbook_service = RetrievalWorkbookService(
        persistence_client=workbook_client, global_persistence_client=workbook_client)
    session_service = RetrievalSessionService(
        persistence_client=workbook_client, global_persistence_client=workbook_client)
    workbook_document = workbook_service.get_workbook_by_id(
        id=workbook_id,
        with_files=True,
        with_sessions=True,
        user_email=request_user,
        is_global=True
    )
    if not workbook_document:
        return make_response(
            {"error": f"No workbook with id: {workbook_id} could be found"}, 404
        )

    new_session = True
    if not session_id:
        workbook_session = session_service.create_workbook_session(
            workbook_id, request_user, [])
    else:
        new_session = False
        workbook_session = session_service.get_session_by_id(
            workbook_id, session_id)

    message = CreateRetrievalSessionQuery(query=prompt)

    created_message = session_service.add_message(
        workbook_id, workbook_session.id, message
    )

    pubsub_message = RetrievalSessionService.to_pubsub_message(
        workbook_user=request_user,
        workbook_id=workbook_document.id,
        session_id=workbook_session.id,
        message=created_message,
        is_public=True
    )
    add_workbook_message(pubsub_message)

    # split here for async

    created_answer = session_service.ask_message(
        workbook_id, prompt, "default", workbook_document.chunk_size, workbook_session.id, new_session, is_global=True)

    pubsub_message = RetrievalSessionService.to_pubsub_message(
        workbook_user=request_user,
        workbook_id=workbook_document.id,
        session_id=workbook_session.id,
        message=created_answer,
        is_public=True
    )
    add_workbook_message(pubsub_message)

    response = make_response(jsonify({
        "workbookSessionId": workbook_session.id,
        "query": created_message.to_dict(True, True, True),
        "answer": created_answer.to_dict(True, True, True)
    }))

    return response


@workbooks.post("/my/<workbook_id>/sessions/<session_id>/feedback/<message_id>/<int:feedback>")
def user_workbook_answer_feedback(workbook_id: str, session_id: str, message_id: str, feedback: bool | int):
    if feedback in [0, 1]:
        feedback = bool(feedback)
    else:
        return {"error": "Feedback out of range"}, 400
    request_user: str = session.get("email")

    workbook_client = FirestoreClient().InstanceClient(user_email=request_user)
    workbook_service = RetrievalWorkbookService(
        persistence_client=workbook_client)
    session_service = RetrievalSessionService(
        persistence_client=workbook_client)
    workbook_document = workbook_service.get_authorized_workbook_by_id(
        id=workbook_id,
        with_files=True,
        with_sessions=True,
        user_email=request_user,
        is_global=False,
    )
    if not workbook_document:
        return make_response(
            {"error": f"No workbook with id: {workbook_id} could be found"}, 404
        )

    fetched_message = session_service.get_message_by_id(
        workbook_id, session_id, message_id
    )
    fetched_message.feedback = feedback
    updated_message = session_service.update_message(
        workbook_id, session_id, fetched_message
    )

    pubsub_message = RetrievalSessionService.to_pubsub_message(
        workbook_user=request_user,
        workbook_id=workbook_document.id,
        session_id=session_id,
        message=updated_message,
    )

    add_workbook_message(pubsub_message)

    response = make_response(
        jsonify(
            {
                "session_id": session_id,
                "answer": updated_message.to_dict(True, True, True),
            }
        )
    )

    return response


@workbooks.post("/my/<workbook_id>/sessions")
def create_user_workbook_session(workbook_id: str):
    request_user: str = session.get("email")
    workbook_client = FirestoreClient().InstanceClient(user_email=request_user)
    workbook_service = RetrievalWorkbookService(
        persistence_client=workbook_client)
    workbook_session_service = RetrievalSessionService(
        persistence_client=workbook_client)

    new_workbook_session: RetrievalSession = workbook_session_service.create_workbook_session(
        workbook_id, request_user, [])

    response_body = {
        "workbookId": workbook_id,
        "session": new_workbook_session.to_dict(True, True, True)
    }

    return make_response(response_body, 200)


@workbooks.post("/public/<workbook_id>/sessions")
def create_global_workbook_session(workbook_id: str):
    request_user: str = session.get("email")
    workbook_client = FirestoreClient().GlobalInstanceClient()
    workbook_service = RetrievalWorkbookService(
        persistence_client=workbook_client, global_persistence_client=workbook_client)
    workbook_session_service = RetrievalSessionService(
        persistence_client=workbook_client, global_persistence_client=workbook_client)

    new_workbook_session: RetrievalSession = workbook_session_service.create_workbook_session(
        workbook_id, request_user, [])

    response_body = {
        "workbookId": workbook_id,
        "session": new_workbook_session.to_dict(True, True, True)
    }

    return make_response(response_body, 200)

# Theres definetly a better way to do the my/public routing. Especially since the only difference is 1 variable as far as I can tell #TODO @preston


@workbooks.post("/public/<workbook_id>/sessions/<session_id>/feedback/<message_id>/<int:feedback>")
def global_workbook_answer_feedback(workbook_id: str, session_id: str, message_id: str, feedback: bool | int):
    if feedback in [0, 1]:
        feedback = bool(feedback)
    else:
        return {"error": "Feedback out of range"}, 400
    request_user: str = session.get("email")

    workbook_client = FirestoreClient().GlobalInstanceClient()
    workbook_service = RetrievalWorkbookService(
        persistence_client=workbook_client)
    session_service = RetrievalSessionService(
        persistence_client=workbook_client)
    workbook_document = workbook_service.get_workbook_by_id(
        id=workbook_id,
        with_files=True,
        with_sessions=True,
        user_email=request_user,
        is_global=True,
    )
    if not workbook_document:
        return make_response(
            {"error": f"No workbook with id: {workbook_id} could be found"}, 404
        )

    fetched_message = session_service.get_message_by_id(
        workbook_id, session_id, message_id
    )
    fetched_message.feedback = feedback
    updated_message = session_service.update_message(
        workbook_id, session_id, fetched_message
    )

    pubsub_message = RetrievalSessionService.to_pubsub_message(
        workbook_user=request_user,
        workbook_id=workbook_document.id,
        session_id=session_id,
        message=updated_message,
        is_public=True
    )

    add_workbook_message(pubsub_message)

    response = make_response(
        jsonify(
            {
                "session_id": session_id,
                "answer": updated_message.to_dict(True, True, True),
            }
        )
    )

    return response

# ===============================================
# Workbook Session History Endpoints
# ===============================================


@workbooks.get("/my/<workbook_id>/sessions")
def get_user_workbook_sessions(workbook_id: str):
    """
    获取用户workbook的所有sessions列表（轻量级）
    返回session基本信息，不包含完整的messages
    """
    user = session.get('email')
    workbook_client = FirestoreClient().InstanceClient(user_email=user)
    workbook_service = RetrievalWorkbookService(
        persistence_client=workbook_client)
    session_service = RetrievalSessionService(
        persistence_client=workbook_client)

    try:
        # 验证workbook权限
        workbook_document = workbook_service.get_authorized_workbook_by_id(
            id=workbook_id,
            user_email=user,
            with_files=False,  # 不需要files信息
            with_sessions=False,  # 不需要在workbook中包含sessions
            is_global=False,
        )
        if not workbook_document:
            return make_response(
                {"error": f"No workbook with id: {workbook_id} could be found"}, 404
            )

        # 获取sessions列表
        sessions = session_service.get_user_sessions_by_workbook_id(
            user_email=user,
            workbook_id=workbook_id
        )

        # 创建轻量级的响应数据
        sessions_summary = []
        for session_obj in sessions:
            # 生成session摘要
            summary = None
            message_count = len(
                session_obj.messages) if session_obj.messages else 0

            if session_obj.messages:
                # 找第一条用户消息作为摘要
                for msg in session_obj.messages:
                    if hasattr(msg, 'query') and msg.query:
                        summary = msg.query[:100] + \
                            "..." if len(msg.query) > 100 else msg.query
                        break

            session_summary = {
                "id": session_obj.id,
                "userId": session_obj.user_id,
                "createdUtc": session_obj.created_utc.isoformat() if hasattr(session_obj, 'created_utc') and session_obj.created_utc else None,
                "messageCount": message_count,
                "summary": summary
            }
            sessions_summary.append(session_summary)

        # 按创建时间倒序排列（最新的在前面）
        sessions_summary.sort(
            key=lambda x: x['createdUtc'] or '', reverse=True)

        response_body = {
            "workbookId": workbook_id,
            "sessions": sessions_summary
        }

        return make_response(jsonify(response_body), 200)

    except Exception as e:
        logger_error.error(
            f"get_user_workbook_sessions: {str(e)} : {traceback.format_exc()}")
        return make_response({"error": "Failed to fetch workbook sessions"}, 500)


@workbooks.get("/public/<workbook_id>/sessions")
def get_global_workbook_sessions(workbook_id: str):
    """
    获取public workbook的所有sessions列表（轻量级）
    返回当前用户在该workbook中的sessions
    """
    user = session.get('email')
    workbook_client = FirestoreClient().GlobalInstanceClient()
    workbook_service = RetrievalWorkbookService(
        persistence_client=workbook_client, global_persistence_client=workbook_client)
    session_service = RetrievalSessionService(
        persistence_client=workbook_client, global_persistence_client=workbook_client)

    try:
        # 验证workbook存在
        workbook_document = workbook_service.get_workbook_by_id(
            id=workbook_id,
            with_files=False,
            with_sessions=False,
            user_email=user,
            is_global=True
        )
        if not workbook_document:
            return make_response(
                {"error": f"No workbook with id: {workbook_id} could be found"}, 404
            )

        # 获取该用户在这个workbook中的sessions
        sessions = session_service.get_user_sessions_by_workbook_id(
            user_email=user,
            workbook_id=workbook_id
        )

        # 创建轻量级的响应数据
        sessions_summary = []
        for session_obj in sessions:
            summary = None
            message_count = len(
                session_obj.messages) if session_obj.messages else 0

            if session_obj.messages:
                for msg in session_obj.messages:
                    if hasattr(msg, 'query') and msg.query:
                        summary = msg.query[:100] + \
                            "..." if len(msg.query) > 100 else msg.query
                        break

            session_summary = {
                "id": session_obj.id,
                "userId": session_obj.user_id,
                "createdUtc": session_obj.created_utc.isoformat() if hasattr(session_obj, 'created_utc') and session_obj.created_utc else None,
                "messageCount": message_count,
                "summary": summary
            }
            sessions_summary.append(session_summary)

        sessions_summary.sort(
            key=lambda x: x['createdUtc'] or '', reverse=True)

        response_body = {
            "workbookId": workbook_id,
            "sessions": sessions_summary
        }

        return make_response(jsonify(response_body), 200)

    except Exception as e:
        logger_error.error(
            f"get_global_workbook_sessions: {str(e)} : {traceback.format_exc()}")
        return make_response({"error": "Failed to fetch workbook sessions"}, 500)


@workbooks.get("/my/<workbook_id>/sessions/<session_id>")
def get_user_workbook_session_details(workbook_id: str, session_id: str):
    """
    获取用户workbook中特定session的完整详情
    返回session的所有messages和聊天记录
    """
    user = session.get('email')
    workbook_client = FirestoreClient().InstanceClient(user_email=user)
    workbook_service = RetrievalWorkbookService(
        persistence_client=workbook_client)
    session_service = RetrievalSessionService(
        persistence_client=workbook_client)

    try:
        # 验证workbook权限
        workbook_document = workbook_service.get_authorized_workbook_by_id(
            id=workbook_id,
            user_email=user,
            with_files=False,
            with_sessions=False,
            is_global=False,
        )
        if not workbook_document:
            return make_response(
                {"error": f"No workbook with id: {workbook_id} could be found"}, 404
            )

        # 获取session详情
        session_obj = session_service.get_session_by_id(
            workbook_id, session_id)
        if not session_obj:
            return make_response(
                {"error": f"No session with id: {session_id} could be found"}, 404
            )

        # 验证session所有权
        if session_obj.user_id != user:
            return make_response(
                {"error": f"No session with id: {session_id} could be found"}, 404
            )

        response_body = session_obj.to_dict(
            date_format_iso=True, include_document_id=True, to_camel=True)
        return make_response(jsonify(response_body), 200)

    except Exception as e:
        logger_error.error(
            f"get_user_workbook_session_details: {str(e)} : {traceback.format_exc()}")
        return make_response(
            {"error": f"No session with id: {session_id} could be found"}, 404
        )


@workbooks.get("/public/<workbook_id>/sessions/<session_id>")
def get_global_workbook_session_details(workbook_id: str, session_id: str):
    """
    获取public workbook中特定session的完整详情
    返回session的所有messages和聊天记录
    """
    user = session.get('email')
    workbook_client = FirestoreClient().GlobalInstanceClient()
    workbook_service = RetrievalWorkbookService(
        persistence_client=workbook_client, global_persistence_client=workbook_client)
    session_service = RetrievalSessionService(
        persistence_client=workbook_client, global_persistence_client=workbook_client)

    try:
        # 验证workbook存在
        workbook_document = workbook_service.get_workbook_by_id(
            id=workbook_id,
            with_files=False,
            with_sessions=False,
            user_email=user,
            is_global=True
        )
        if not workbook_document:
            return make_response(
                {"error": f"No workbook with id: {workbook_id} could be found"}, 404
            )

        # 获取session详情
        session_obj = session_service.get_session_by_id(
            workbook_id, session_id)
        if not session_obj:
            return make_response(
                {"error": f"No session with id: {session_id} could be found"}, 404
            )

        # 验证session所有权
        if session_obj.user_id != user:
            return make_response(
                {"error": f"No session with id: {session_id} could be found"}, 404
            )

        response_body = session_obj.to_dict(
            date_format_iso=True, include_document_id=True, to_camel=True)
        return make_response(jsonify(response_body), 200)

    except Exception as e:
        logger_error.error(
            f"get_global_workbook_session_details: {str(e)} : {traceback.format_exc()}")
        return make_response(
            {"error": f"No session with id: {session_id} could be found"}, 404
        )
